from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, EventEndpointRoutes
from services.data_service.api.mappers.eventv3_api_output_mapper import EventV3APIOutputMapper
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.models.request.event.delete_event_api_request_input import DeleteEventAPIRequestInput
from services.data_service.api.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.models.request.event.modify_event_assets_api_request_input import (
    ModifyEventAssetsAPIRequestInput,
)
from services.data_service.api.models.request.event.update_event_api_request_input import UpdateEventAPIRequestInput
from services.data_service.api.models.request.feed.event_feed_api_request_input import EventFeedAPIRequestInput
from services.data_service.api.models.response.feed.event_feed_api_response import EventFeedAPIResponse
from services.data_service.application.use_cases.event_feed.event_feed_input_boundary import EventFeedInputBoundary
from services.data_service.application.use_cases.event_feed.event_feed_use_case import EventFeedUseCase
from services.data_service.application.use_cases.events.delete_event_by_id_use_case import DeleteEventByIdUseCase
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import (
    DeleteEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundary,
)
from services.data_service.application.use_cases.events.models.update_event_input_boundary import (
    UpdateEventInputBoundary,
)
from services.data_service.application.use_cases.events.modify_event_assets_use_case import ModifyEventAssetsUseCase
from services.data_service.application.use_cases.events.update_event_use_case import UpdateEventUseCase
from services.data_service.v1.api.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.v1.api.event_api_output_mapper import EventAPIOutputMapper

event_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.EVENT}",
    tags=["event"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@event_router.post(
    EventEndpointRoutes.FEED,
    response_model=EventFeedAPIResponse,
)
async def event_feed_endpoint(
    event_feed_use_case: EventFeedUseCase = Injected(EventFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: EventFeedInputBoundary = Depends(EventFeedAPIRequestInput.to_input_boundary),
):
    """
    The endpoint allows iteration through user documents which are sorted by document timestamp in descending order.
    The feed is paginated,
     and each page contains a continuation token that can be used to request the next page of results.
    The initial call can define data_type and organization filters,
     which must then stay immutable for subsequent calls with continuation token returned.
    The limit and range parameters can be mutated to fine scope the next request.
    """
    try:
        result = await event_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return EventFeedAPIResponse(
            items=[EventAPIOutputMapper.map(document=e) for e in result.events],
            continuation_token=ContinuationTokenMarshaller.encode_event_feed_continuation_token(
                result.continuation_token
            ),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.post(EventEndpointRoutes.BASE)
async def insert_event_endpoint(
    request_input: InsertEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertEventUseCase = Injected(InsertEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    """
    Create new events in the system.

    This endpoint allows users to insert one or more events into their personal data collection.
    Events can be of various types including core events, notes, symptoms, emotions, medications,
    sleep records, and more. Each event must include a name, timestamp, and type-specific data.

    **Request Body:**
    - `documents`: Array of event objects to insert (minimum 1 required)
    - Each event must include:
      - `name`: Event name (1-200 characters)
      - `timestamp`: ISO 8601 datetime when the event occurred
      - `type`: Event type (e.g., "core_event", "note", "symptom")
      - Type-specific fields (category, rating, etc.)
    - Optional fields:
      - `end_time`: For events with duration
      - `tags`: Array of string tags for categorization
      - `note`: Additional text description
      - `assets`: Array of file attachments
      - `template_id`: UUID of template used to create event

    **Response:**
    - `documents`: Array of created event objects with generated IDs and system properties

    **HTTP Status Codes:**
    - 200: Events successfully created
    - 400: Invalid request data or duplicate events detected
    - 401: Authentication required
    - 422: Validation error in request body

    **Example:**
    ```json
    {
      "documents": [{
        "name": "Morning headache",
        "timestamp": "2024-01-15T08:30:00Z",
        "type": "symptom",
        "category": "headache",
        "rating": 6,
        "body_parts": ["head"],
        "tags": ["morning", "stress"]
      }]
    }
    ```
    """
    try:
        events = await use_case.execute_async(
            boundary=InsertEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(EventEndpointRoutes.BASE)
async def update_event_endpoint(
    request_input: UpdateEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: UpdateEventUseCase = Injected(UpdateEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    """
    Update existing events in the system.

    This endpoint allows users to modify one or more existing events in their personal data collection.
    Users can update event properties such as name, timestamp, ratings, categories, and other
    type-specific fields. The event ID and type must be provided to identify which events to update.

    **Request Body:**
    - `documents`: Array of event objects to update (minimum 1 required)
    - Each event must include:
      - `id`: UUID of the existing event to update
      - `name`: Updated event name (1-200 characters)
      - `timestamp`: Updated ISO 8601 datetime
      - `type`: Event type (must match existing event type)
      - Type-specific fields to update (category, rating, etc.)
    - Optional fields:
      - `end_time`: Updated end time for events with duration
      - `tags`: Updated array of string tags
      - `plan_extension`: Updated plan extension data

    **Response:**
    - `documents`: Array of updated event objects with modified fields and updated system properties

    **HTTP Status Codes:**
    - 200: Events successfully updated
    - 400: Invalid request data, duplicate events, or operation not allowed
    - 401: Authentication required
    - 404: One or more events not found
    - 422: Validation error in request body

    **Example:**
    ```json
    {
      "documents": [{
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "Updated headache severity",
        "timestamp": "2024-01-15T08:30:00Z",
        "type": "symptom",
        "category": "headache",
        "rating": 8,
        "tags": ["morning", "severe"]
      }]
    }
    ```
    """
    try:
        events = await use_case.execute_async(
            boundary=UpdateEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(EventEndpointRoutes.MODIFY_ASSETS)
async def modify_event_assets_endpoint(
    request_input: ModifyEventAssetsAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ModifyEventAssetsUseCase = Injected(ModifyEventAssetsUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    """
    Modify asset attachments for existing events.

    This endpoint allows users to add, remove, or update file attachments (assets) associated
    with existing events. Assets can include images, audio recordings, videos, or other files
    that provide additional context or evidence for the event.

    **Request Body:**
    - `documents`: Array of event asset modification objects (minimum 1 required)
    - Each modification object must include:
      - `id`: UUID of the existing event to modify
      - `type`: Event type (must match existing event type)
      - `assets`: Array of asset operations to perform
    - Asset operations can include:
      - Adding new assets with base64-encoded data
      - Removing existing assets by asset ID
      - Updating asset metadata

    **Supported Asset Types:**
    - Images: JPEG, PNG, GIF, WebP
    - Audio: MP3, WAV, M4A, OGG
    - Video: MP4, MOV, AVI, WebM
    - Documents: PDF, TXT

    **Response:**
    - `documents`: Array of updated event objects with modified asset references

    **HTTP Status Codes:**
    - 200: Event assets successfully modified
    - 400: Invalid request data, duplicate events, or unsupported asset format
    - 401: Authentication required
    - 404: Event not found
    - 413: Asset file too large (max 10MB per asset)
    - 422: Validation error in request body

    **Example:**
    ```json
    {
      "documents": [{
        "id": "123e4567-e89b-12d3-a456-************",
        "type": "symptom",
        "assets": [{
          "asset_type": "image",
          "data": "base64-encoded-image-data...",
          "filename": "headache_photo.jpg"
        }]
      }]
    }
    ```
    """
    try:
        events = await use_case.execute_async(
            boundary=ModifyEventAssetsInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.delete(EventEndpointRoutes.BASE)
async def delete_event_endpoint(
    request_input: DeleteEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteEventByIdUseCase = Injected(DeleteEventByIdUseCase),
) -> CommonDocumentsIdsResponse:
    """
    Delete existing events from the system.

    This endpoint allows users to permanently remove one or more events from their personal
    data collection. Once deleted, events cannot be recovered. Users must provide the event
    ID and type to identify which events to delete.

    **Request Body:**
    - `documents`: Array of event deletion objects (minimum 1 required)
    - Each deletion object must include:
      - `id`: UUID of the existing event to delete
      - `type`: Event type (must match existing event type for verification)

    **Response:**
    - `document_ids`: Array of UUIDs of successfully deleted events

    **HTTP Status Codes:**
    - 200: Events successfully deleted
    - 400: Invalid request data or operation not allowed
    - 401: Authentication required
    - 404: One or more events not found
    - 422: Validation error in request body

    **Important Notes:**
    - Deletion is permanent and cannot be undone
    - Associated assets (files) will also be deleted
    - Events that are part of a group may have restrictions on deletion
    - Users can only delete their own events

    **Example:**
    ```json
    {
      "documents": [{
        "id": "123e4567-e89b-12d3-a456-************",
        "type": "symptom"
      }, {
        "id": "456e7890-e89b-12d3-a456-************",
        "type": "note"
      }]
    }
    ```
    """
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
